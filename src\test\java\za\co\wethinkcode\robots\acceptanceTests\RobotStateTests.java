package za.co.wethinkcode.robots.acceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.server.RobotWorldClient;
import za.co.wethinkcode.robots.server.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player
 * I want to check the state of my robot
 * So that I can see its status, health and position in the world
 */
public class RobotStateTests {
    private final static int DEFAULT_PORT = 5002;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private static Thread serverThread;

    @BeforeAll
    static void startServer() throws Exception {
        // Check if we should use external server (for reference server testing)
        String useExternalServer = System.getProperty("test.use.external.server", "false");

        if (!"true".equals(useExternalServer)) {
            // Start our own server only if not using external server
            serverThread = new Thread(() -> {
                try {
                    za.co.wethinkcode.robots.server.Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "1"});
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            serverThread.setDaemon(true);  // Make it a daemon thread so JVM can exit
            serverThread.start();

            // Give server a moment to start
            Thread.sleep(2000);
        } else {
            // When using external server, just wait a bit to ensure it's ready
            Thread.sleep(500);
        }
    }

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }



    @Test
    void getStateWhenRobotExistsShouldSucceed() {
        // if the robot is successfully launched
        String launchRequest = "{" +
                "  \"robot\": \"R2D2\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"shooter\", \"5\", \"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
        assertEquals("OK", launchResponse.get("result").asText());

        // When I send a state request for that robot
        String stateRequest = "{" +
                "  \"robot\": \"R2D2\"," +
                "  \"command\": \"state\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode stateResponse = serverClient.sendRequest(stateRequest);

        //  state response
        assertEquals("OK", stateResponse.get("result").asText());
        assertNotNull(stateResponse.get("state"));
        assertNotNull(stateResponse.get("data"));
        assertNotNull(stateResponse.get("data").get("position"));
    }

    @Test
    void getStateWhenRobotDoesNotExistShouldFail() {
        //  robot does not exist
        assertTrue(serverClient.isConnected());

        
        String request = "{" +
                "  \"robot\": \"NOTEXIST\"," +
                "  \"command\": \"state\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().toLowerCase().contains("not found")
                || response.get("data").get("message").asText().toLowerCase().contains("does not exist"));
    }
}
