package za.co.wethinkcode.robots.acceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.server.RobotWorldClient;
import za.co.wethinkcode.robots.server.RobotWorldJsonClient;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Story: Move Backward
 * As a player
 * I want to command my robot to move backward a specified number of steps
 * So that I can tactically retreat and navigate the world.
 */
class MoveBackwardTests {
    private final static int DEFAULT_PORT = 5004;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private static Thread serverThread;

    @BeforeAll
    static void startServer() throws Exception {
        // Check if we should use external server (for reference server testing)
        String useExternalServer = System.getProperty("test.use.external.server", "false");

        if (!"true".equals(useExternalServer)) {
            // Start our own server only if not using external server
            serverThread = new Thread(() -> {
                try {
                    za.co.wethinkcode.robots.server.Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "1"});
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            serverThread.setDaemon(true);
            serverThread.start();

            Thread.sleep(2000);
        } else {
            // When using external server, just wait a bit to ensure it's ready
            Thread.sleep(500);
        }
    }

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    void moveBackwardAtEdgeShouldReturnSouthEdge() {
        assertTrue(serverClient.isConnected());
        String robotName = "HAL_" + UUID.randomUUID();

        JsonNode launchResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}");

        assertNotNull(launchResponse);
        assertEquals("OK", launchResponse.get("result").asText());

        JsonNode moveResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"back\"," +
                "\"arguments\": [\"5\"]" +
                "}");

        assertNotNull(moveResponse);
        assertEquals("OK", moveResponse.get("result").asText());
        assertTrue(moveResponse.get("data").get("message").asText().contains("SOUTH edge"));
        assertEquals(0, moveResponse.get("data").get("position").get(0).asInt());
        assertEquals(0, moveResponse.get("data").get("position").get(1).asInt());
    }

    @Test
    void moveBackwardWithZeroSteps() {
        assertTrue(serverClient.isConnected());
        String robotName = "HAL_" + UUID.randomUUID();

        JsonNode launchResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}");

        assertNotNull(launchResponse);
        assertEquals("OK", launchResponse.get("result").asText());

        JsonNode response = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"back\"," +
                "\"arguments\": [\"0\"]" +
                "}");

        assertNotNull(response);
        if (response.get("result").asText().equals("OK")) {
            assertEquals(0, response.get("data").get("position").get(0).asInt());
            assertEquals(0, response.get("data").get("position").get(1).asInt());
        } else {
            assertEquals("ERROR", response.get("result").asText());
            assertTrue(response.get("message").asText().toLowerCase().contains("steps must be a positive number"));
        }
    }
}
