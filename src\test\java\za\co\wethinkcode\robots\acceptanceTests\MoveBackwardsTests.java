package za.co.wethinkcode.robots.acceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.server.RobotWorldClient;
import za.co.wethinkcode.robots.server.RobotWorldJsonClient;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Story: Move Backward
 * As a player
 * I want to command my robot to move backward a specified number of steps
 * So that I can tactically retreat and navigate the world.
 */
class MoveBackwardTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private static Thread serverThread;

    @BeforeAll
    static void startServer() throws Exception {
        // Check if we should start our own server (default is to use external server)
        String startOwnServer = System.getProperty("test.start.own.server", "false");

        if ("true".equals(startOwnServer)) {
            // Start our own server only if explicitly requested
            serverThread = new Thread(() -> {
                try {
                    za.co.wethinkcode.robots.server.Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "1"});
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            serverThread.setDaemon(true);
            serverThread.start();

            Thread.sleep(2000);
        } else {
            // Default: assume external server is running, just wait a bit to ensure it's ready
            Thread.sleep(500);
        }
    }

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    void moveBackwardAtEdgeShouldReturnSouthEdge() {
        assertTrue(serverClient.isConnected());
        String robotName = "HAL_" + UUID.randomUUID();

        JsonNode launchResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}");

        assertNotNull(launchResponse);
        assertEquals("OK", launchResponse.get("result").asText());

        JsonNode moveResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"back\"," +
                "\"arguments\": [\"5\"]" +
                "}");

        assertNotNull(moveResponse);
        // Handle different response formats between reference server and own server
        if (moveResponse.has("result")) {
            assertEquals("OK", moveResponse.get("result").asText());
            if (moveResponse.has("data") && moveResponse.get("data").has("message")) {
                assertTrue(moveResponse.get("data").get("message").asText().contains("SOUTH edge"));
            }
            if (moveResponse.has("data") && moveResponse.get("data").has("position")) {
                assertEquals(0, moveResponse.get("data").get("position").get(0).asInt());
                assertEquals(0, moveResponse.get("data").get("position").get(1).asInt());
            }
        } else {
            // Reference server might use different format - just check it's not an error
            assertFalse(moveResponse.has("error") || moveResponse.get("result").asText().equals("ERROR"));
        }
    }

    @Test
    void moveBackwardWithZeroSteps() {
        assertTrue(serverClient.isConnected());
        String robotName = "HAL_" + UUID.randomUUID();

        JsonNode launchResponse = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}");

        assertNotNull(launchResponse);
        assertEquals("OK", launchResponse.get("result").asText());

        JsonNode response = serverClient.sendRequest("{" +
                "\"robot\": \"" + robotName + "\"," +
                "\"command\": \"back\"," +
                "\"arguments\": [\"0\"]" +
                "}");

        assertNotNull(response);
        // Handle different response formats between reference server and own server
        if (response.has("result")) {
            if (response.get("result").asText().equals("OK")) {
                if (response.has("data") && response.get("data").has("position")) {
                    assertEquals(0, response.get("data").get("position").get(0).asInt());
                    assertEquals(0, response.get("data").get("position").get(1).asInt());
                }
            } else {
                assertEquals("ERROR", response.get("result").asText());
                // Check for error message in different possible locations
                String errorMessage = "";
                if (response.has("message")) {
                    errorMessage = response.get("message").asText().toLowerCase();
                } else if (response.has("data") && response.get("data").has("message")) {
                    errorMessage = response.get("data").get("message").asText().toLowerCase();
                }
                // Only check error message if we found one
                if (!errorMessage.isEmpty()) {
                    assertTrue(errorMessage.contains("steps must be a positive number") ||
                              errorMessage.contains("invalid") ||
                              errorMessage.contains("zero"));
                }
            }
        } else {
            // Reference server might use different format - just ensure it's not null
            assertNotNull(response);
        }
    }
}
